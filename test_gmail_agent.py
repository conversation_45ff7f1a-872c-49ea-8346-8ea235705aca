#!/usr/bin/env python3
"""
Test script for the Gmail AI Agent with Mistral Agents API
"""

import os
import sys
from unittest.mock import Mock, patch
import json

# Add the current directory to the path so we can import gmail
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gmail import GmailAIAgent

def test_agent_initialization():
    """Test that the agent initializes properly with Mistral client"""
    print("Testing agent initialization...")

    # Mock the Mistral client and Gmail authentication
    with patch('gmail.Mistral') as mock_mistral, \
         patch('gmail.build') as mock_build, \
         patch('gmail.Credentials') as mock_creds, \
         patch('os.path.exists') as mock_exists:

        mock_client = Mock()
        mock_mistral.return_value = mock_client

        # Mock the agent creation response
        mock_agent = Mock()
        mock_agent.id = "test_agent_id"
        mock_client.beta.agents.create.return_value = mock_agent

        # Mock Gmail service
        mock_service = Mock()
        mock_build.return_value = mock_service

        # Mock credentials
        mock_exists.return_value = False  # No existing token file

        try:
            with patch.object(GmailAIAgent, '_authenticate') as mock_auth:
                mock_auth.return_value = None

                agent = GmailAIAgent(
                    credentials_file='test_credentials.json',
                    mistral_api_key='test_key'
                )

                # Check that the agent was initialized
                assert agent.mistral_client == mock_client
                assert agent.service is None  # Service should be None due to mocked auth

                print("✓ Agent initialization test passed")
                return True

        except Exception as e:
            print(f"✗ Agent initialization test failed: {e}")
            return False

def test_parse_query():
    """Test the new _parse_query method with Mistral agent"""
    print("Testing query parsing...")

    with patch('gmail.Mistral') as mock_mistral, \
         patch('gmail.build') as mock_build, \
         patch('os.path.exists') as mock_exists:

        mock_client = Mock()
        mock_mistral.return_value = mock_client

        # Mock agent creation
        mock_agent = Mock()
        mock_agent.id = "test_agent_id"
        mock_client.beta.agents.create.return_value = mock_agent

        # Mock chat completion response
        mock_response = Mock()
        mock_choice = Mock()
        mock_message = Mock()
        mock_message.content = "from:john subject:(project meeting) newer_than:7d"
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        mock_client.chat.complete.return_value = mock_response

        # Mock Gmail service
        mock_service = Mock()
        mock_build.return_value = mock_service
        mock_exists.return_value = False

        try:
            with patch.object(GmailAIAgent, '_authenticate') as mock_auth:
                mock_auth.return_value = None

                agent = GmailAIAgent(
                    credentials_file='test_credentials.json',
                    mistral_api_key='test_key'
                )

                # Test query parsing
                result = agent._parse_query("Find emails from John about project meetings this week")

                # Check that the result contains the expected Gmail query
                assert 'q' in result
                assert result['q'] == "from:john subject:(project meeting) newer_than:7d"

                print("✓ Query parsing test passed")
                return True

        except Exception as e:
            print(f"✗ Query parsing test failed: {e}")
            return False

def test_fallback_parsing():
    """Test fallback query parsing when agent is not available"""
    print("Testing fallback query parsing...")

    with patch('gmail.Mistral') as mock_mistral, \
         patch('gmail.build') as mock_build, \
         patch('os.path.exists') as mock_exists:

        mock_client = Mock()
        mock_mistral.return_value = mock_client

        # Mock agent creation to fail
        mock_client.beta.agents.create.side_effect = Exception("Agent creation failed")

        # Mock Gmail service
        mock_service = Mock()
        mock_build.return_value = mock_service
        mock_exists.return_value = False

        try:
            with patch.object(GmailAIAgent, '_authenticate') as mock_auth:
                mock_auth.return_value = None

                agent = GmailAIAgent(
                    credentials_file='test_credentials.json',
                    mistral_api_key='test_key'
                )

                # Test that agent was created despite Mistral connection issues
                assert agent.mistral_client == mock_client

                # Test fallback query parsing
                result = agent._parse_query("Find <NAME_EMAIL> about meetings today")

                # Check that basic pattern matching worked
                assert 'q' in result
                assert 'from:<EMAIL>' in result['q']
                assert 'newer_than:1d' in result['q']

                print("✓ Fallback query parsing test passed")
                return True

        except Exception as e:
            print(f"✗ Fallback query parsing test failed: {e}")
            return False

def test_smart_search():
    """Test the enhanced smart_search method"""
    print("Testing smart search...")

    with patch('gmail.Mistral') as mock_mistral, \
         patch('gmail.build') as mock_build, \
         patch('os.path.exists') as mock_exists:

        mock_client = Mock()
        mock_mistral.return_value = mock_client

        # Mock agent creation
        mock_agent = Mock()
        mock_agent.id = "test_agent_id"
        mock_client.beta.agents.create.return_value = mock_agent

        # Mock chat completion response for smart search
        mock_response = Mock()
        mock_choice = Mock()
        mock_message = Mock()
        mock_message.content = json.dumps({
            "gmail_query": "is:important newer_than:3d",
            "interpretation": "Looking for important emails from the last 3 days"
        })
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        mock_client.chat.complete.return_value = mock_response

        # Mock Gmail service
        mock_service = Mock()
        mock_build.return_value = mock_service
        mock_exists.return_value = False

        try:
            with patch.object(GmailAIAgent, '_authenticate') as mock_auth:
                mock_auth.return_value = None

                agent = GmailAIAgent(
                    credentials_file='test_credentials.json',
                    mistral_api_key='test_key'
                )

                # Mock the search_emails and generate_summary methods
                with patch.object(agent, 'search_emails') as mock_search, \
                     patch.object(agent, 'generate_summary') as mock_summary:

                    mock_search.return_value = []
                    mock_summary.return_value = "No emails found"

                    # Test smart search
                    result = agent.smart_search("Show me important emails from the last few days")

                    # Check that the result has the expected structure
                    assert 'original_query' in result
                    assert 'ai_interpretation' in result
                    assert 'enhanced_query' in result
                    assert 'total_emails' in result
                    assert 'summary' in result

                    print("✓ Smart search test passed")
                    return True

        except Exception as e:
            print(f"✗ Smart search test failed: {e}")
            return False

def main():
    """Run all tests"""
    print("Running Gmail AI Agent tests with Mistral Agents API...\n")
    
    tests = [
        test_agent_initialization,
        test_parse_query,
        test_fallback_parsing,
        test_smart_search
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The Mistral Agents API integration is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
