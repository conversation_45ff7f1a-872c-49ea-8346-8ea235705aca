# Gmail AI Agent with Mistral AI - Requirements
# Core Google API dependencies
google-auth==2.23.4
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.1.1
google-api-python-client==2.108.0

# Mistral AI SDK
mistralai==0.4.2

# Natural Language Processing
nltk==3.8.1
spacy==3.7.2
# Note: After installation, run: python -m spacy download en_core_web_sm

# Data handling and utilities
requests==2.31.0
python-dateutil==2.8.2

# Optional: For better email parsing
beautifulsoup4==4.12.2
lxml==4.9.3

# Development and testing (optional)
pytest==7.4.3
pytest-mock==3.12.0
python-dotenv==1.0.0

# Type hints and code quality (optional)
mypy==1.7.1
black==23.11.0
flake8==6.1.0

# Logging and monitoring (optional)
structlog==23.2.0
rich==13.7.0