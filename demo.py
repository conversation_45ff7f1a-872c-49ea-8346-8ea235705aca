#!/usr/bin/env python3
"""
Demo script for the simplified Gmail AI Agent
"""

import os
from gmail import GmailAIAgent

def main():
    """Demonstrate the Gmail AI Agent functionality"""
    print("Gmail AI Agent Demo")
    print("==================")
    
    # Check for required environment variables
    if not os.getenv('MISTRAL_API_KEY'):
        print("❌ MISTRAL_API_KEY environment variable not set")
        print("Please set your Mistral API key in a .env file or environment variable")
        return
    
    if not os.path.exists('credentials.json'):
        print("❌ Gmail credentials.json file not found")
        print("Please download your Gmail API credentials from Google Cloud Console")
        return
    
    try:
        # Initialize the agent
        print("🔧 Initializing Gmail AI Agent...")
        agent = GmailAIAgent()
        print("✅ Agent initialized successfully!")
        
        # Demo queries
        demo_queries = [
            "Find <NAME_EMAIL> about meetings this week",
            "Show me important emails from today",
            "Find emails with attachments about invoices",
            "Get unread emails from last month",
            "Find emails about project updates"
        ]
        
        print("\n📧 Testing Query Parsing:")
        print("-" * 40)
        
        for query in demo_queries:
            print(f"\nQuery: {query}")
            try:
                result = agent._parse_query(query)
                print(f"Gmail Search: {result['q']}")
            except Exception as e:
                print(f"Error: {e}")
        
        print("\n🔍 Testing Smart Search:")
        print("-" * 40)
        
        try:
            smart_result = agent.smart_search("Show me important emails from the last few days")
            print(f"Original Query: {smart_result['original_query']}")
            print(f"Enhanced Query: {smart_result['enhanced_query']}")
            print(f"Total Emails Found: {smart_result['total_emails']}")
            print(f"Summary: {smart_result['summary']}")
        except Exception as e:
            print(f"Smart search error: {e}")
        
        print("\n✅ Demo completed successfully!")
        
    except FileNotFoundError as e:
        print(f"❌ File not found: {e}")
        print("Make sure you have the required credentials file")
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
