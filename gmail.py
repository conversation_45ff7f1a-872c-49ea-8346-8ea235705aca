import os
import base64
import json
import re
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from mistralai import Mistral
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from dotenv import load_dotenv

load_dotenv()

@dataclass
class EmailMessage:
    """Data class to represent an email message"""
    id: str
    thread_id: str
    subject: str
    sender: str
    recipient: str
    date: datetime
    snippet: str
    body: str
    labels: List[str]
    attachments: List[Dict[str, Any]]

class GmailAIAgent:
    """
    A comprehensive Gmail AI agent that can be queried with natural language
    to find emails and generate summaries.
    """
    
    # Gmail API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    
    def __init__(self, credentials_file: Optional[str] = None,
                 token_file: Optional[str] = None, mistral_api_key: Optional[str] = None):
        """
        Initialize the Gmail AI Agent

        Args:
            credentials_file: Path to Gmail API credentials JSON file
            token_file: Path to store OAuth2 token
            mistral_api_key: Mistral AI API key for natural language processing
        """
        # Use .env variables if not provided
        self.credentials_file = credentials_file or os.getenv('GMAIL_CREDENTIALS_FILE', './credentials.json')
        self.token_file = token_file or os.getenv('GMAIL_TOKEN_FILE', 'token.json')
        self.service = None

        #Initialize Mistral AI client
        if mistral_api_key:
            self.mistral_client = Mistral(api_key=mistral_api_key)
        else:
            api_key = os.getenv('MISTRAL_API_KEY')
            if not api_key:
                raise ValueError("Mistral API key must be provided either as parameter or MISTRAL_API_KEY environment variable")
            self.mistral_client = Mistral(api_key=api_key)

        # Test Mistral client connection
        self._test_mistral_connection()

        #Authenticate and build Gmail service
        self._authenticate()
    
    def _test_mistral_connection(self):
        """Test Mistral client connection"""
        try:
            # Simple test to verify the client works
            response = self.mistral_client.chat.complete(
                model="mistral-small-latest",
                messages=[{"role": "user", "content": "test"}],
                max_tokens=5
            )
            print("Mistral AI connection successful")

        except Exception as error:
            print(f"Warning: Mistral AI connection failed: {error}")
            print("Gmail search will use basic pattern matching")
    
    def _authenticate(self):
        """Authenticate with Gmail API using OAuth2"""
        try:
            creds = None

            # Load existing token
            if os.path.exists(self.token_file):
                creds = Credentials.from_authorized_user_file(self.token_file, self.SCOPES)

            # If no valid credentials, get new ones
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                else:
                    if not os.path.exists(self.credentials_file):
                        raise FileNotFoundError(f"Gmail credentials file not found: {self.credentials_file}")

                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_file, self.SCOPES)
                    creds = flow.run_local_server(port=0)

                # Save credentials for next run
                with open(self.token_file, 'w') as token:
                    token.write(creds.to_json())

            # Build Gmail service
            self.service = build('gmail', 'v1', credentials=creds)

            # Test the connection
            profile = self.service.users().getProfile(userId='me').execute()
            print(f"Gmail API authentication successful for: {profile.get('emailAddress', 'Unknown')}")

        except Exception as e:
            print(f"Gmail authentication failed: {e}")
            self.service = None
            raise
    
    def _parse_query(self, query: str) -> Dict[str, Any]:
        """
        Parse natural language query into Gmail search parameters using Mistral AI

        Args:
            query: Natural language query

        Returns:
            Dictionary with parsed search parameters
        """
        params = {
            'q': '',
            'maxResults': 50,
            'includeSpamTrash': False
        }

        try:
            # Use Mistral AI to convert natural language to Gmail search syntax
            response = self.mistral_client.chat.complete(
                model="mistral-large-latest",
                messages=[
                    {
                        "role": "system",
                        "content": """Convert natural language queries to Gmail search syntax. Use these operators:
- Time: newer_than:1d, older_than:1d, newer_than:7d, newer_than:30d
- Sender: from:<EMAIL> or from:name
- Subject: subject:(keywords)
- Labels: is:important, is:unread, is:starred, in:drafts, in:sent, in:spam, in:trash
- Attachments: has:attachment
- Keywords: combine with OR for general search

Return only the Gmail search query string, nothing else."""
                    },
                    {
                        "role": "user",
                        "content": f"Convert to Gmail search: {query}"
                    }
                ],
                max_tokens=100,
                temperature=0.1
            )

            if response.choices and response.choices[0].message.content:
                content = response.choices[0].message.content
                if isinstance(content, str):
                    params['q'] = content.strip()
                else:
                    params['q'] = str(content).strip()
                return params

        except Exception as error:
            print(f"AI query parsing failed: {error}")
            print("Falling back to basic pattern matching")

        # Fallback to basic pattern matching
        params['q'] = self._fallback_query_parsing(query)
        return params

    def _fallback_query_parsing(self, query: str) -> str:
        """Fallback query parsing using pattern matching"""
        query_lower = query.lower()
        query_parts = []

        # Time-based queries
        time_patterns = {
            r'today|this day': 'newer_than:1d',
            r'yesterday': 'older_than:1d newer_than:2d',
            r'this week|past week|last week': 'newer_than:7d',
            r'this month|past month|last month': 'newer_than:30d',
            r'this year|past year|last year': 'newer_than:365d'
        }

        for pattern, gmail_query in time_patterns.items():
            if re.search(pattern, query_lower):
                query_parts.append(gmail_query)
                break

        # Sender queries
        sender_match = re.search(r'from:(\S+)|from\s+(\S+)|sent by\s+(\S+)', query_lower)
        if sender_match:
            sender = sender_match.group(1) or sender_match.group(2) or sender_match.group(3)
            query_parts.append(f'from:{sender}')

        # Subject queries
        subject_match = re.search(r'subject:([^"]+)|about\s+([^"]+)', query_lower)
        if subject_match:
            subject = subject_match.group(1) or subject_match.group(2)
            query_parts.append(f'subject:({subject.strip()})')

        # Label/category queries
        label_patterns = {
            r'important|priority': 'is:important',
            r'unread': 'is:unread',
            r'starred?': 'is:starred',
            r'draft': 'in:drafts',
            r'sent': 'in:sent',
            r'spam': 'in:spam',
            r'trash': 'in:trash'
        }

        for pattern, gmail_query in label_patterns.items():
            if re.search(pattern, query_lower):
                query_parts.append(gmail_query)

        # Attachment queries
        if re.search(r'attachment|file|pdf|doc|image', query_lower):
            query_parts.append('has:attachment')

        # Extract keywords for general search
        words = query_lower.split()
        common_words = {'the', 'and', 'or', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'like', 'from'}
        keywords = [word for word in words if word.isalpha() and word not in common_words and len(word) > 2]

        # Add keywords to search query
        if keywords:
            keyword_query = ' OR '.join(keywords[:5])
            query_parts.append(f'({keyword_query})')

        return ' '.join(query_parts)

    def _build_gmail_query(self, components: List[str]) -> str:
        """Build a clean Gmail query from components"""
        query_parts = []
        for component in components:
            if component and component.strip():
                query_parts.append(component.strip())

        return ' '.join(query_parts)

    def _get_message_details(self, message_id: str) -> Optional[EmailMessage]:
        """
        Get detailed information about a specific email message
        
        Args:
            message_id: Gmail message ID
            
        Returns:
            EmailMessage object with detailed information
        """
        if self.service is None:
            raise RuntimeError("Gmail API service is not initialized. Please check authentication.")
        try:
            message = self.service.users().messages().get(
                userId='me', id=message_id, format='full').execute()
            
            headers = message['payload'].get('headers', [])
            header_dict = {h['name']: h['value'] for h in headers}
            
            # Extract email body
            body = self._extract_body(message['payload'])
            
            # Parse date
            date_str = header_dict.get('Date', '')
            try:
                date = datetime.strptime(date_str, '%a, %d %b %Y %H:%M:%S %z')
            except:
                date = datetime.now()
            
            # Extract attachments
            attachments = self._extract_attachments(message['payload'])
            
            return EmailMessage(
                id=message['id'],
                thread_id=message['threadId'],
                subject=header_dict.get('Subject', 'No Subject'),
                sender=header_dict.get('From', 'Unknown'),
                recipient=header_dict.get('To', 'Unknown'),
                date=date,
                snippet=message.get('snippet', ''),
                body=body,
                labels=message.get('labelIds', []),
                attachments=attachments
            )
        
        except HttpError as error:
            print(f'An error occurred: {error}')
            return None
    
    def _extract_body(self, payload: Dict) -> str:
        """Extract email body from payload"""
        body = ""
        
        if 'parts' in payload:
            for part in payload['parts']:
                if part['mimeType'] == 'text/plain':
                    if 'data' in part['body']:
                        body = base64.urlsafe_b64decode(
                            part['body']['data']).decode('utf-8')
                        break
                elif part['mimeType'] == 'text/html' and not body:
                    if 'data' in part['body']:
                        body = base64.urlsafe_b64decode(
                            part['body']['data']).decode('utf-8')
        else:
            if payload['mimeType'] == 'text/plain':
                if 'data' in payload['body']:
                    body = base64.urlsafe_b64decode(
                        payload['body']['data']).decode('utf-8')
        
        return body
    
    def _extract_attachments(self, payload: Dict) -> List[Dict[str, Any]]:
        """Extract attachment information from payload"""
        attachments = []
        
        def extract_parts(parts):
            for part in parts:
                if part.get('filename'):
                    attachments.append({
                        'filename': part['filename'],
                        'mimeType': part['mimeType'],
                        'size': part['body'].get('size', 0)
                    })
                if 'parts' in part:
                    extract_parts(part['parts'])
        
        if 'parts' in payload:
            extract_parts(payload['parts'])
        
        return attachments
    
    def search_emails(self, query: str) -> List[EmailMessage]:
        """
        Search emails using natural language query
        
        Args:
            query: Natural language search query
            
        Returns:
            List of EmailMessage objects matching the query
        """
        # Parse natural language query
        search_params = self._parse_query(query)
        
        if self.service is None:
            raise RuntimeError("Gmail API service is not initialized. Please check authentication.")
        try:
            # Search for messages
            results = self.service.users().messages().list(
                userId='me', **search_params).execute()
            
            messages = results.get('messages', [])
            
            # Get detailed information for each message
            email_messages = []
            for msg in messages:
                email_msg = self._get_message_details(msg['id'])
                if email_msg:
                    email_messages.append(email_msg)
            
            return email_messages
        
        except HttpError as error:
            print(f'An error occurred: {error}')
            return []
    
    def generate_summary(self, emails: List[EmailMessage], 
                         summary_type: str = "general", 
                         model: str = "mistral-large-latest") -> str:
        """
        Generate AI-powered summary of emails using Mistral AI
        
        Args:
            emails: List of EmailMessage objects
            summary_type: Type of summary ("general", "detailed", "brief")
            model: Mistral model to use (mistral-large-latest, mistral-medium-latest, etc.)
            
        Returns:
            Generated summary text
        """
        if not emails:
            return "No emails found for the given query."
        
        # Prepare email data for summarization
        email_data = []
        for email in emails:
            email_info = {
                'subject': email.subject,
                'sender': email.sender,
                'date': email.date.strftime('%Y-%m-%d %H:%M'),
                'snippet': email.snippet,
                'body_preview': email.body[:500] if email.body else email.snippet
            }
            email_data.append(email_info)
        
        # Create prompt based on summary type
        if summary_type == "brief":
            prompt = f"""Provide a brief summary of these {len(emails)} emails:

{json.dumps(email_data, indent=2)}

Summary should be 2-3 sentences highlighting the main themes and important messages."""
        
        elif summary_type == "detailed":
            prompt = f"""Provide a detailed analysis of these {len(emails)} emails:

{json.dumps(email_data, indent=2)}

Include:
1. Main themes and topics
2. Key senders and their messages
3. Important dates and deadlines
4. Action items or requests
5. Overall context and relationships between emails"""
        
        else: # general
            prompt = f"""Summarize these {len(emails)} emails in a clear, organized way:

{json.dumps(email_data, indent=2)}

Provide a balanced summary that covers the main points, key senders, and important information without being too brief or too verbose."""
        
        try:
            # Get response from Mistral AI
            response = self.mistral_client.chat.complete(
                model=model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an AI assistant that helps summarize and analyze emails. Provide clear, organized, and useful summaries."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=1000,
                temperature=0.3
            )

            content = response.choices[0].message.content
            if isinstance(content, str):
                return content.strip()
            elif content:
                return str(content).strip()
            else:
                return "No summary generated"
            
        except Exception as error:
            print(f"An error occurred during summarization: {error}")
            return f"Error generating summary: {error}"
            
    def analyze_email_content(self, emails: List[EmailMessage], 
                             analysis_type: str = "sentiment",
                             model: str = "mistral-large-latest") -> Dict[str, Any]:
        """
        Perform advanced analysis on email content using Mistral AI
        
        Args:
            emails: List of EmailMessage objects
            analysis_type: Type of analysis ("sentiment", "priority", "topics", "action_items")
            model: Mistral model to use
            
        Returns:
            Dictionary with analysis results
        """
        if not emails:
            return {"error": "No emails provided for analysis"}
        
        # Prepare email content
        email_content = []
        for email in emails:
            email_content.append({
                'id': email.id,
                'subject': email.subject,
                'sender': email.sender,
                'content': email.body[:1000] if email.body else email.snippet
            })
        
        # Create analysis prompt based on type
        if analysis_type == "sentiment":
            prompt = f"""Analyze the sentiment of these {len(emails)} emails. For each email, provide:
1. Overall sentiment (positive, negative, neutral)
2. Confidence score (0-1)
3. Key emotional indicators

Emails:
{json.dumps(email_content, indent=2)}"""
        
        elif analysis_type == "priority":
            prompt = f"""Analyze the priority level of these {len(emails)} emails. For each email, provide:
1. Priority level (high, medium, low)
2. Urgency indicators
3. Reason for priority assessment

Emails:
{json.dumps(email_content, indent=2)}"""
        
        elif analysis_type == "topics":
            prompt = f"""Extract and categorize the main topics from these {len(emails)} emails:
1. List the main topics/themes
2. Group emails by topic
3. Identify trending subjects

Emails:
{json.dumps(email_content, indent=2)}"""
        
        elif analysis_type == "action_items":
            prompt = f"""Identify action items and tasks from these {len(emails)} emails:
1. Extract specific tasks or requests
2. Identify deadlines or time-sensitive items
3. Categorize by urgency and importance

Emails:
{json.dumps(email_content, indent=2)}"""
        
        else:
            return {"error": f"Unknown analysis type: {analysis_type}"}
        
        try:
            response = self.mistral_client.chat.complete(
                model=model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert email analyst. Provide structured, actionable insights from email content."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=1500,
                temperature=0.2
            )

            content = response.choices[0].message.content
            analysis_text = content if isinstance(content, str) else str(content) if content else "No analysis generated"

            return {
                'analysis_type': analysis_type,
                'model_used': model,
                'total_emails': len(emails),
                'analysis': analysis_text.strip()
            }
        
        except Exception as error:
            return {"error": f"Analysis failed: {error}"}
    
    def smart_search(self, natural_query: str,
                     model: str = "mistral-large-latest") -> Dict[str, Any]:
        """
        Use Mistral AI to interpret complex natural language queries and convert to Gmail search

        Args:
            natural_query: Complex natural language query
            model: Mistral model to use for query interpretation

        Returns:
            Enhanced search results with AI interpretation
        """
        # Use Mistral AI to enhance query understanding
        interpretation_prompt = f"""Convert this natural language email query into specific search parameters and provide context:

Query: "{natural_query}"

Provide:
1. Gmail search string
2. Interpretation of user intent
3. Suggested refinements or alternatives
4. Expected result types

Format as JSON with keys: gmail_query, intent, suggestions, expected_results"""
        
        try:
            response = self.mistral_client.chat.complete(
                model=model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at interpreting email search queries and converting them to Gmail search syntax."
                    },
                    {
                        "role": "user",
                        "content": interpretation_prompt
                    }
                ],
                max_tokens=500,
                temperature=0.1
            )
            
            # Try to parse the JSON response
            try:
                content = response.choices[0].message.content
                content_str = content if isinstance(content, str) else str(content) if content else "{}"
                ai_interpretation = json.loads(content_str)
                enhanced_query = ai_interpretation.get('gmail_query', natural_query)
            except:
                enhanced_query = natural_query
                ai_interpretation = {"error": "Could not parse AI response"}

            # Perform the search
            emails = self.search_emails(enhanced_query)

            # Generate summary
            summary = self.generate_summary(emails, "general", model)

            return {
                'original_query': natural_query,
                'ai_interpretation': ai_interpretation,
                'enhanced_query': enhanced_query,
                'total_emails': len(emails),
                'summary': summary,
                'emails': [
                    {
                        'subject': email.subject,
                        'sender': email.sender,
                        'date': email.date.isoformat(),
                        'snippet': email.snippet
                    } for email in emails
                ]
            }
            
        except Exception as error:
            try:
                # Fallback to regular search
                emails = self.search_emails(natural_query)
                summary = self.generate_summary(emails, "general", model)
                return {
                    'original_query': natural_query,
                    'ai_interpretation': {"error": "AI interpretation failed"},
                    'enhanced_query': natural_query,
                    'total_emails': len(emails),
                    'summary': summary,
                    'emails': [
                        {
                            'subject': email.subject,
                            'sender': email.sender,
                            'date': email.date.isoformat(),
                            'snippet': email.snippet
                        } for email in emails
                    ]
                }
            except Exception as fallback_error:
                return {
                    'original_query': natural_query,
                    'error': f"Error processing request: {str(error)}. Fallback also failed: {str(fallback_error)}",
                    'total_emails': 0,
                    'summary': "No summary available due to errors",
                    'emails': []
                }
    
    def query_emails(self, query: str, summary_type: str = "general", 
                     model: str = "mistral-large-latest") -> Dict[str, Any]:
        """
        Main method to query emails and generate summary
        
        Args:
            query: Natural language query
            summary_type: Type of summary to generate
            model: Mistral model to use for summarization
            
        Returns:
            Dictionary with search results and summary
        """
        # Search for emails
        emails = self.search_emails(query)
        
        # Generate summary
        summary = self.generate_summary(emails, summary_type, model)
        
        # Prepare response
        response = {
            'query': query,
            'total_emails': len(emails),
            'summary': summary,
            'model_used': model,
            'emails': []
        }
        
        # Add email details
        for email in emails:
            email_dict = {
                'subject': email.subject,
                'sender': email.sender,
                'date': email.date.isoformat(),
                'snippet': email.snippet,
                'labels': email.labels,
                'attachments': len(email.attachments)
            }
            response['emails'].append(email_dict)
        
        return response
    
    def get_email_analytics(self, days: int = 30) -> Dict[str, Any]:
        """
        Get analytics about email patterns
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with email analytics
        """
        # Search for recent emails
        query = f"newer_than:{days}d"

        # Ensure Gmail API service is initialized
        if self.service is None:
            self._authenticate()
            if self.service is None:
                return {'error': 'Gmail API service is not initialized. Please check authentication.'}
        
        try:
            results = self.service.users().messages().list(
                userId='me', q=query, maxResults=500).execute()
            
            messages = results.get('messages', [])
            
            # Analyze patterns
            senders = {}
            subjects = {}
            daily_count = {}
            
            for msg in messages:
                email = self._get_message_details(msg['id'])
                if email:
                    # Count by sender
                    sender = email.sender.split('<')[0].strip()
                    senders[sender] = senders.get(sender, 0) + 1
                    
                    # Count by subject keywords
                    subject_words = email.subject.lower().split()
                    common_words = {'the', 'and', 'or', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'like', 'from'}
                    for word in subject_words:
                        if len(word) > 3 and word not in common_words:
                            subjects[word] = subjects.get(word, 0) + 1
                    
                    # Count by day
                    day = email.date.strftime('%Y-%m-%d')
                    daily_count[day] = daily_count.get(day, 0) + 1
            
            # Get top items
            top_senders = sorted(senders.items(), key=lambda x: x[1], reverse=True)[:10]
            top_subjects = sorted(subjects.items(), key=lambda x: x[1], reverse=True)[:10]
            
            return {
                'period_days': days,
                'total_emails': len(messages),
                'average_per_day': len(messages) / days,
                'top_senders': top_senders,
                'top_subject_words': top_subjects,
                'daily_counts': daily_count
            }
        
        except HttpError as error:
            return {'error': str(error)}

# Example usage and testing
if __name__ == "__main__":
    agent = GmailAIAgent()
    result = agent.smart_search("Show me important emails from the last few days")
    print(result)
