#!/usr/bin/env python3
"""
Simple test script for the simplified Gmail AI Agent
"""

import os
import sys
from unittest.mock import Mock, patch

# Add the current directory to the path so we can import gmail
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """Test basic functionality without real API calls"""
    print("Testing simplified Gmail AI Agent...")
    
    # Mock all external dependencies
    with patch('gmail.Mistral') as mock_mistral, \
         patch('gmail.build') as mock_build, \
         patch('os.path.exists') as mock_exists:
        
        # Setup mocks
        mock_client = Mock()
        mock_mistral.return_value = mock_client
        
        # Mock chat completion response
        mock_response = Mock()
        mock_choice = Mock()
        mock_message = Mock()
        mock_message.content = "from:john subject:(meeting) newer_than:7d"
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        mock_client.chat.complete.return_value = mock_response
        
        # Mock Gmail service
        mock_service = Mock()
        mock_build.return_value = mock_service
        mock_exists.return_value = False
        
        try:
            from gmail import GmailAIAgent
            
            # Mock authentication to avoid file requirements
            with patch.object(GmailAIAgent, '_authenticate') as mock_auth:
                mock_auth.return_value = None
                
                # Create agent
                agent = GmailAIAgent(mistral_api_key='test_key')
                
                # Test query parsing
                result = agent._parse_query("Find emails from John about meetings this week")
                
                print(f"✓ Query parsing successful: {result['q']}")
                
                # Test fallback parsing
                with patch.object(agent.mistral_client.chat, 'complete', side_effect=Exception("API Error")):
                    fallback_result = agent._parse_query("Find <NAME_EMAIL> about meetings today")
                    print(f"✓ Fallback parsing successful: {fallback_result['q']}")
                
                print("✓ All basic tests passed!")
                return True
                
        except Exception as e:
            print(f"✗ Test failed: {e}")
            return False

def test_query_building():
    """Test the query building helper"""
    print("Testing query building...")
    
    with patch('gmail.Mistral') as mock_mistral, \
         patch('gmail.build') as mock_build, \
         patch('os.path.exists') as mock_exists:
        
        mock_client = Mock()
        mock_mistral.return_value = mock_client
        mock_service = Mock()
        mock_build.return_value = mock_service
        mock_exists.return_value = False
        
        try:
            from gmail import GmailAIAgent
            
            with patch.object(GmailAIAgent, '_authenticate') as mock_auth:
                mock_auth.return_value = None
                
                agent = GmailAIAgent(mistral_api_key='test_key')
                
                # Test query building
                components = ["from:john", "subject:(meeting)", "newer_than:7d"]
                query = agent._build_gmail_query(components)
                
                expected = "from:john subject:(meeting) newer_than:7d"
                assert query == expected, f"Expected '{expected}', got '{query}'"
                
                print(f"✓ Query building successful: {query}")
                return True
                
        except Exception as e:
            print(f"✗ Query building test failed: {e}")
            return False

def main():
    """Run all tests"""
    print("Running simplified Gmail AI Agent tests...\n")
    
    tests = [
        test_basic_functionality,
        test_query_building
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The simplified implementation is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
